# 辅助写作模式文件上传功能实现说明

## 功能概述

在辅助写作模式下添加了文件上传功能，用户可以上传文件并在调用 sessions 接口时传递文件信息。

## 实现的功能

### 1. 界面显示
- 在辅助写作模式下，联网按钮旁边显示文件上传按钮
- 联网模式下文件上传按钮被禁用（不可上传）
- 不联网模式下文件上传按钮可用（可以上传）
- 上传文件后显示文件信息（文件名、大小、删除按钮）
- 显示提示信息：仅支持上传1个文件，上传新文件将替换当前文件

### 2. 文件限制
- 只能上传1个文件
- 支持的文件格式：PDF (.pdf)、Word (.doc, .docx)、文本 (.txt)、Markdown (.md)
- 文件大小限制：10MB
- 上传新文件会自动替换现有文件

### 3. 文件管理
- 可以通过删除按钮移除已上传的文件
- 切换到其他模式时自动清空已上传的文件
- 开启新对话时自动清空已上传的文件

### 4. API 集成
- 在调用 sessions 接口时，如果有上传的文件，会在请求数据中添加 `file` 字段
- `file` 字段的值为文件名
- 只在辅助写作模式 (`content_creation`) 下传递此字段
- **重要**：文件上传后会自动重新调用创建会话接口，确保文件参数传递给后端
- 文件移除后也会重新调用创建会话接口，移除文件参数

## 代码修改详情

### 1. 模板修改 (src/views/chatRoom.vue)

#### 添加文件上传按钮
```html
<!-- 文件上传按钮 - 只在辅助写作模式下显示 -->
<div class="file-upload-selector" v-if="isWifi">
    <input type="file" ref="chatFileInput" style="display: none" 
        @change="handleFileSelect" accept=".pdf,.doc,.docx,.txt,.md">
    <v-btn variant="outlined" color="primary" size="small" density="compact"
        :disabled="isProcessing || isNetworkEnabled" @click="triggerFileUpload" 
        class="file-upload-btn">
        <v-icon size="x-small" class="mr-1">mdi-paperclip</v-icon>
        {{ uploadedFile ? '替换文件' : '上传文件' }}
    </v-btn>
</div>
```

#### 添加已上传文件显示
```html
<!-- 已上传文件显示 -->
<div class="uploaded-files" v-if="uploadedFile && isWifi">
    <div class="uploaded-file-item">
        <v-icon size="small" color="primary" class="mr-1">
            {{ getFileIcon(uploadedFile.name) }}
        </v-icon>
        <span class="file-name">{{ uploadedFile.name }}</span>
        <span class="file-size">{{ formatFileSize(uploadedFile.size) }}</span>
        <v-btn icon size="x-small" variant="text" color="error" 
            @click="removeUploadedFile" class="ml-1" 
            :disabled="isProcessing">
            <v-icon size="small">mdi-close-circle</v-icon>
        </v-btn>
    </div>
    <div class="file-upload-hint">
        <v-icon size="x-small" color="grey" class="mr-1">mdi-information-outline</v-icon>
        <span>仅支持上传1个文件，上传新文件将替换当前文件</span>
    </div>
</div>
```

### 2. JavaScript 修改

#### 添加状态变量
```javascript
// 文件上传相关状态
const uploadedFile = ref(null);
const chatFileInput = ref(null);
```

#### 添加文件上传方法
```javascript
// 文件上传相关方法
const triggerFileUpload = () => {
    if (chatFileInput.value) {
        chatFileInput.value.click();
    }
};

const handleFileSelect = async (event) => {
    // 文件选择和验证逻辑
    // ...文件验证代码...

    // 文件上传后，重新创建会话以传递文件参数
    try {
        console.log('文件上传完成，重新创建会话...');
        isProcessing.value = true;
        await createSession(false); // 不清空消息列表，只重新创建会话
        console.log('会话重新创建成功，文件参数已传递');
    } catch (error) {
        console.error('重新创建会话失败:', error);
        showToast('文件上传成功，但会话更新失败', 'warning');
    } finally {
        isProcessing.value = false;
    }
};

const removeUploadedFile = async () => {
    uploadedFile.value = null;
    console.log('文件已移除');
    showToast('文件已移除', 'info');

    // 文件移除后，重新创建会话以移除文件参数
    try {
        console.log('文件已移除，重新创建会话...');
        isProcessing.value = true;
        await createSession(false); // 不清空消息列表，只重新创建会话
        console.log('会话重新创建成功，文件参数已移除');
    } catch (error) {
        console.error('重新创建会话失败:', error);
        showToast('文件已移除，但会话更新失败', 'warning');
    } finally {
        isProcessing.value = false;
    }
};

const getFileIcon = (fileName) => {
    // 根据文件扩展名返回对应图标
};
```

#### 修改 sessions 接口调用
在 `createSession` 函数中的辅助写作模式处理部分：
```javascript
} else if (selectedMode.value.value === 'content_creation') {
    // 内容创作模式添加type参数，根据联网状态决定
    requestData.type = isNetworkEnabled.value ? 'intel' : 'local';
    if (kbId !== null) {
        requestData.kbId = String(kbId);
    } else {
        requestData.kbId = null;
    }
    // 添加文件参数
    if (uploadedFile.value) {
        requestData.file = uploadedFile.value.name;
        console.log('内容创作模式，添加文件参数:', requestData.file);
    }
    console.log('内容创作模式，使用kbId:', requestData.kbId, 'type:', requestData.type);
}
```

#### 添加文件清理逻辑
- 在 `switchToMode` 函数中，切换到非辅助写作模式时清空文件
- 在 `createNewSession` 函数中，创建新会话时清空文件

### 3. CSS 样式修改 (src/styles/chat-view.css)

```css
/* 文件上传相关样式 */
.file-upload-selector {
    margin-left: 8px;
}

.file-upload-btn {
    min-width: auto !important;
    height: 32px !important;
    font-size: 12px !important;
}

.uploaded-files {
    margin-left: 8px;
    margin-top: 8px;
}

.uploaded-file-item {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 12px;
    color: #666;
    max-width: 200px;
}

.file-upload-hint {
    display: flex;
    align-items: center;
    margin-top: 4px;
    font-size: 10px;
    color: #999;
    line-height: 1.2;
}
```

## 使用说明

1. 切换到辅助写作模式
2. 确保联网状态为关闭（不联网模式）
3. 点击"上传文件"按钮选择文件
4. 文件上传后会显示在界面上，**同时自动重新创建会话**
5. 可以点击删除按钮移除文件，**同时自动重新创建会话**
6. 可以上传新文件替换现有文件，**每次都会重新创建会话**
7. 开启新会话时，sessions 接口会包含 file 字段（如果有上传文件）

## 会话重新创建机制

- **文件上传时**：自动调用 `createSession(false)` 重新创建会话，保留现有消息
- **文件移除时**：自动调用 `createSession(false)` 重新创建会话，保留现有消息
- **处理状态**：在重新创建会话期间，显示"处理中..."状态，禁用相关按钮
- **错误处理**：如果会话重新创建失败，显示警告提示但不影响文件操作

## 注意事项

- 文件上传功能仅在辅助写作模式下可用
- 联网模式下不允许上传文件
- 只能上传1个文件，新文件会替换旧文件
- 切换模式或开启新对话会清空已上传的文件
- 文件信息通过 sessions 接口的 file 字段传递给后端
