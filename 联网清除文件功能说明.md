# 联网清除文件功能说明

## 🎯 功能需求

当用户在辅助写作模式下点击联网按钮开启联网时：
1. 自动清除已上传的文件
2. 重新调用 sessions 接口（不包含 file 字段）
3. 显示相应的用户提示

## 🔧 技术实现

### 修改 toggleNetwork 函数

```javascript
// 切换联网状态
const toggleNetwork = async () => {
    const wasNetworkEnabled = isNetworkEnabled.value;
    isNetworkEnabled.value = !isNetworkEnabled.value;
    console.log('联网状态切换为:', isNetworkEnabled.value ? '联网' : '不联网');
    
    // 如果开启了联网，需要清除已上传的文件
    if (isNetworkEnabled.value && uploadedFile.value) {
        console.log('开启联网，清除已上传的文件:', uploadedFile.value.name);
        uploadedFile.value = null;
        
        // 重新调用 sessions 接口
        try {
            console.log('联网开启，重新创建会话...');
            isProcessing.value = true;
            await createSession(false); // 不清空消息列表，只重新创建会话
            console.log('会话重新创建成功，文件参数已移除');
            showToast('已开启联网，文件已移除', 'info');
        } catch (error) {
            console.error('重新创建会话失败:', error);
            showToast('已开启联网，但会话更新失败', 'warning');
        } finally {
            isProcessing.value = false;
        }
    } else {
        // 关闭联网或没有文件时，只显示状态提示
        showToast(`已${isNetworkEnabled.value ? '开启' : '关闭'}联网`, 'info');
    }
};
```

### 界面状态更新

```html
<v-btn :variant="isNetworkEnabled ? 'flat' : 'outlined'"
    :color="isNetworkEnabled ? 'success' : 'grey'" size="small" density="compact"
    :disabled="isProcessing" @click="toggleNetwork" class="network-btn">
    <v-icon size="x-small" class="mr-1">
        {{ isProcessing ? 'mdi-loading' : (isNetworkEnabled ? 'mdi-wifi' : 'mdi-wifi-off') }}
    </v-icon>
    {{ isProcessing ? '处理中...' : '联网' }}
</v-btn>
```

## 🔄 执行流程

### 场景1：有文件时开启联网
1. 用户点击联网按钮
2. 检测到有已上传文件
3. 清除 `uploadedFile.value = null`
4. 设置 `isProcessing = true`
5. 调用 `createSession(false)` 重新创建会话
6. Sessions 接口不包含 file 字段
7. 显示成功提示："已开启联网，文件已移除"
8. 设置 `isProcessing = false`

### 场景2：无文件时开启联网
1. 用户点击联网按钮
2. 检测到没有已上传文件
3. 直接显示提示："已开启联网"

### 场景3：关闭联网
1. 用户点击联网按钮
2. 关闭联网状态
3. 显示提示："已关闭联网"
4. 用户可以重新上传文件

## 🎨 用户体验

### 视觉反馈
- 处理中显示加载图标（旋转动画）
- 按钮文本变为"处理中..."
- 按钮禁用状态防止重复点击

### 提示信息
- 有文件时开启联网：`"已开启联网，文件已移除"`
- 无文件时开启联网：`"已开启联网"`
- 关闭联网：`"已关闭联网"`
- 错误情况：`"已开启联网，但会话更新失败"`

## 🔍 调试验证

### 控制台日志
```
联网状态切换为: 联网
开启联网，清除已上传的文件: test.txt
联网开启，重新创建会话...
会话重新创建成功，文件参数已移除
```

### 网络请求验证
1. 打开开发者工具 Network 标签
2. 上传文件后开启联网
3. 查看新的 sessions 接口请求
4. 验证请求体中不包含 file 字段

### 界面状态验证
1. 上传文件后，文件显示在界面上
2. 点击联网按钮
3. 按钮显示"处理中..."和加载图标
4. 文件从界面上消失
5. 显示成功提示

## ⚠️ 注意事项

### 数据一致性
- 确保文件清除和会话更新的原子性
- 错误情况下保持界面状态一致

### 用户体验
- 提供明确的反馈信息
- 防止用户在处理中重复操作
- 错误处理不影响基本功能

### 性能考虑
- 异步处理避免界面阻塞
- 合理的超时处理
- 内存及时释放

## 📋 测试用例

### 测试步骤
1. 切换到辅助写作模式
2. 关闭联网状态
3. 上传一个文件
4. 验证文件显示在界面上
5. 点击联网按钮开启联网
6. 验证文件从界面消失
7. 验证显示"已开启联网，文件已移除"提示
8. 验证 sessions 接口被调用且不包含 file 字段

### 边界情况测试
1. 无文件时开启联网
2. 已开启联网时再次点击
3. 网络错误时的处理
4. 快速连续点击的处理

## 🚀 功能总结

现在联网按钮具有智能文件管理功能：
- **开启联网** = 自动清除文件 + 重新创建会话
- **关闭联网** = 允许文件上传
- **处理状态** = 防止重复操作 + 视觉反馈
- **错误处理** = 友好提示 + 状态恢复

这确保了联网模式和文件上传模式的互斥性，提供了清晰的用户体验。
