# 测试文件上传功能

这是一个用于测试文件上传功能的 Markdown 文件。

## 功能要求

1. 在辅助写作模式下显示文件上传按钮
2. 联网模式下不能上传文件
3. 不联网模式下可以上传文件
4. 只能上传1个文件
5. 上传新文件会替换现有文件
6. 支持的文件格式：PDF、Word、TXT、Markdown
7. 文件大小限制：10MB
8. **重要**：调用 sessions 接口时传递 file 字段（包含文件内容，不是文件名）
   - 文本文件：传递文本内容
   - 二进制文件：传递 Base64 编码内容

## 测试步骤

1. 切换到辅助写作模式
2. 确认显示联网按钮和文件上传按钮
3. 在联网状态下，文件上传按钮应该被禁用
4. 在不联网状态下，文件上传按钮应该可用
5. 点击上传文件按钮，选择文件
6. 确认文件显示在界面上
7. **验证会话重新创建**：观察控制台日志，确认调用了 sessions 接口
8. **测试联网清除文件**：点击联网按钮，确认文件被自动清除
9. 再次关闭联网，上传文件，确认替换现有文件并重新创建会话
10. 点击删除按钮，确认文件被移除并重新创建会话
11. 切换到其他模式，确认文件被清空
12. 开启新对话，确认文件被清空

## 关键验证点

### 会话重新创建验证
- 文件上传后，控制台应显示："文件上传完成，重新创建会话..."
- 文件移除后，控制台应显示："文件已移除，重新创建会话..."
- 按钮应显示"处理中..."状态
- sessions 接口应包含 file 字段（有文件时）或不包含 file 字段（无文件时）

### 网络请求验证
- 打开浏览器开发者工具的 Network 标签
- 上传文件后，应看到新的 sessions 接口调用
- 请求体中应包含 file 字段，**内容为文件内容**：
  - 文本文件（.txt, .md）：直接文本内容
  - 二进制文件（.pdf, .doc, .docx）：Base64 编码，格式如 `data:application/pdf;base64,JVBERi0x...`
- 移除文件后，应看到新的 sessions 接口调用
- 请求体中不应包含 file 字段

### 文件内容验证
- 上传文本文件，检查 file 字段是否为纯文本
- 上传 PDF 文件，检查 file 字段是否为 Base64 编码
- 验证文件内容长度是否合理
- 确认不同文件类型的处理方式正确

### 联网清除文件验证
- 上传文件后，点击联网按钮
- 控制台应显示："开启联网，清除已上传的文件: xxx"
- 控制台应显示："联网开启，重新创建会话..."
- 界面上的文件应该消失
- 应显示提示："已开启联网，文件已移除"
- 新的 sessions 接口调用不应包含 file 字段
- 联网按钮应显示"处理中..."状态（带旋转图标）
