# 测试文件上传功能

这是一个用于测试文件上传功能的 Markdown 文件。

## 功能要求

1. 在辅助写作模式下显示文件上传按钮
2. 联网模式下不能上传文件
3. 不联网模式下可以上传文件
4. 只能上传1个文件
5. 上传新文件会替换现有文件
6. 支持的文件格式：PDF、Word、TXT、Markdown
7. 文件大小限制：10MB
8. 调用 sessions 接口时传递 file 字段

## 测试步骤

1. 切换到辅助写作模式
2. 确认显示联网按钮和文件上传按钮
3. 在联网状态下，文件上传按钮应该被禁用
4. 在不联网状态下，文件上传按钮应该可用
5. 点击上传文件按钮，选择文件
6. 确认文件显示在界面上
7. **验证会话重新创建**：观察控制台日志，确认调用了 sessions 接口
8. 再次上传文件，确认替换现有文件并重新创建会话
9. 点击删除按钮，确认文件被移除并重新创建会话
10. 切换到其他模式，确认文件被清空
11. 开启新对话，确认文件被清空

## 关键验证点

### 会话重新创建验证
- 文件上传后，控制台应显示："文件上传完成，重新创建会话..."
- 文件移除后，控制台应显示："文件已移除，重新创建会话..."
- 按钮应显示"处理中..."状态
- sessions 接口应包含 file 字段（有文件时）或不包含 file 字段（无文件时）

### 网络请求验证
- 打开浏览器开发者工具的 Network 标签
- 上传文件后，应看到新的 sessions 接口调用
- 请求体中应包含 file 字段
- 移除文件后，应看到新的 sessions 接口调用
- 请求体中不应包含 file 字段
