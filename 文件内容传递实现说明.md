# 文件内容传递实现说明

## 🎯 核心变更

**重要**：根据需求，`file` 字段现在传递的是**文件内容**，而不是文件名。

## 📁 文件内容读取机制

### 文件类型处理
- **文本文件**（.txt, .md）：直接读取为 UTF-8 文本
- **二进制文件**（.pdf, .doc, .docx）：读取为 Base64 编码字符串

### 实现代码
```javascript
// 读取文件内容
const readFileContent = (file) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            const result = e.target.result;
            const extension = file.name.split('.').pop().toLowerCase();
            
            if (extension === 'txt' || extension === 'md') {
                // 文本文件直接返回文本内容
                console.log('文本文件读取成功，内容长度:', result.length);
                resolve(result);
            } else {
                // 二进制文件返回 Base64 编码
                console.log('二进制文件读取成功，Base64 长度:', result.length);
                resolve(result);
            }
        };
        
        reader.onerror = (e) => {
            console.error('文件读取失败:', e);
            reject(new Error('文件读取失败'));
        };
        
        // 根据文件类型选择读取方式
        const extension = file.name.split('.').pop().toLowerCase();
        if (extension === 'txt' || extension === 'md') {
            // 文本文件直接读取为文本
            reader.readAsText(file, 'UTF-8');
        } else if (extension === 'pdf' || extension === 'doc' || extension === 'docx') {
            // 二进制文件读取为 Base64
            reader.readAsDataURL(file);
        } else {
            // 默认按文本处理
            reader.readAsText(file, 'UTF-8');
        }
    });
};
```

## 🔄 文件上传流程

### 1. 文件选择和验证
```javascript
const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (file) {
        // 文件大小和类型验证
        // ...验证逻辑...
        
        try {
            // 读取文件内容
            console.log('开始读取文件内容...');
            const fileContent = await readFileContent(file);
            
            // 存储文件信息和内容
            uploadedFile.value = {
                name: file.name,
                size: file.size,
                file: file,
                content: fileContent // 关键：存储文件内容
            };
            
            // 重新创建会话传递文件内容
            isProcessing.value = true;
            await createSession(false);
            console.log('会话重新创建成功，文件内容已传递');
        } catch (error) {
            console.error('读取文件失败:', error);
            showToast('文件读取失败', 'error');
        } finally {
            isProcessing.value = false;
        }
    }
};
```

### 2. Sessions 接口调用
```javascript
// 在 createSession 函数中
if (selectedMode.value.value === 'content_creation') {
    requestData.type = isNetworkEnabled.value ? 'intel' : 'local';
    requestData.kbId = kbId ? String(kbId) : null;
    
    // 添加文件内容参数（不是文件名）
    if (uploadedFile.value && uploadedFile.value.content) {
        requestData.file = uploadedFile.value.content; // 传递文件内容
        console.log('内容创作模式，添加文件内容，文件名:', uploadedFile.value.name, '内容长度:', uploadedFile.value.content.length);
    }
}
```

## 📊 数据格式示例

### 文本文件（.txt, .md）
```javascript
// 文件内容直接为文本
requestData.file = "这是文件的文本内容...";
```

### 二进制文件（.pdf, .doc, .docx）
```javascript
// 文件内容为 Base64 编码
requestData.file = "data:application/pdf;base64,JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjIgMCBvYmoKPDwKL1R5cGUgL1BhZ2VzCi9LaWRzIFszIDAgUl0KL0NvdW50IDEKPD4KZW5kb2JqCjMgMCBvYmoKPDwKL1R5cGUgL1BhZ2UK...";
```

## 🔍 调试和验证

### 控制台日志
- 文件读取开始：`"开始读取文件内容..."`
- 文件读取成功：`"文本文件读取成功，内容长度: 1234"`
- 会话创建：`"内容创作模式，添加文件内容，文件名: test.txt, 内容长度: 1234"`

### 网络请求验证
1. 打开浏览器开发者工具 Network 标签
2. 上传文件后查看 sessions 接口请求
3. 在请求体中查看 `file` 字段
4. 验证内容格式：
   - 文本文件：直接文本内容
   - 二进制文件：`data:application/xxx;base64,` 开头的 Base64 字符串

## ⚠️ 注意事项

### 文件大小限制
- 由于要传递完整文件内容，建议保持 10MB 限制
- Base64 编码会增加约 33% 的数据量
- 大文件可能影响网络传输性能

### 内存使用
- 文件内容会完整加载到内存中
- 大文件可能影响浏览器性能
- 建议在生产环境中监控内存使用

### 错误处理
- 文件读取失败时显示错误提示
- 会话创建失败时显示警告
- 保持界面状态一致性

## 🚀 使用流程

1. 切换到辅助写作模式
2. 关闭联网状态
3. 点击"上传文件"按钮
4. 选择文件（系统自动读取文件内容）
5. 系统自动重新创建会话，传递文件内容
6. 后端接收到包含完整文件内容的 sessions 请求

现在 `file` 字段包含的是完整的文件内容，后端可以直接处理文件数据，而不需要额外的文件上传步骤。
