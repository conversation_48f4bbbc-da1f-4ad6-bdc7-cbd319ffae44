/* 加载模态框样式 */
:deep(.custom-loading-modal .el-loading-mask) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

:deep(.cancel-btn-container) {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

:deep(.cancel-upload-modal-btn) {
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
    font-weight: 500;
}

:deep(.cancel-upload-modal-btn:hover) {
    background-color: #ff7875;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);
}

.file-type-icon {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 返回顶部按钮样式 */
.custom-backtop {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    background-color: #1677ff;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.12);
    border-radius: 50%;
    transition: all 0.3s;
}

.custom-backtop:hover {
    background-color: #4096ff;
    transform: scale(1.1);
}

/* 应用容器 */
.app-container {
    display: flex;
    /* flex-wrap: wrap; */
    /* min-height: 100vh; */
    width: 100%;
    position: relative;
    /* overflow-x: hidden; */
    /* height: 100%; */
    /* overflow: auto; */
}

/* 整体布局样式 */
.left-sidebar {
    position: relative;
    width: 80px;
    display: flex;
    flex-direction: column;
    z-index: 100;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    /* height: 92vh; */
    border-right: none;
}

/* 场景面板样式 - 竖向布局 */
.scenarios-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    height: 100%;
    width: 80px;
}

/* 应用Logo */
.app-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
    padding: 16px 0;
}

.app-name {
    color: white;
    font-size: 14px;
    font-weight: 600;
    margin-top: 8px;
    letter-spacing: 1px;
}

/* 场景按钮容器 */
.scenario-buttons {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 24px;
    align-items: center;
    width: 100%;
    padding: 10px 0;
}

/* 场景按钮样式 */
.scenario-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 50px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: transparent;
    border: none;
    position: relative;
}

.scenario-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.scenario-button.active {
    background: rgba(255, 255, 255, 0.2);
}

.scenario-button.active::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 30px;
    background: white;
    border-radius: 2px;
}

.scenario-label {
    color: white;
    font-size: 10px;
    font-weight: 500;
    margin-top: 2px;
    text-align: center;
    line-height: 1.1;
    max-width: 55px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 底部按钮 */
.bottom-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: transparent;
    border-radius: 8px;
    margin-top: auto;
    margin-bottom: 20px;
}

.bottom-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.bottom-label {
    color: white;
    font-size: 10px;
    font-weight: 500;
    margin-top: 2px;
    text-align: center;
    line-height: 1.2;
}

.right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 94vh;
    padding: 0;
    margin-left: 0;
    transition: margin-left 0.3s ease;
}

/* 当显示场景历史记录时，右侧内容区域需要留出空间 */
.right-content.with-scenario-history {
    margin-left: 320px;
}

.main-container123 {
    margin-left: 60px;
    margin-bottom: 40px;
    min-height: 600px;
}

/* 知识库导航菜单样式 */
.sidebar-menu {
    width: 100%;
    overflow-y: auto;
    padding: 0px 2px;
    background-color: #fff;
    flex: 0 0 auto;
    height: calc(100vh - 70px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.create-btn-container {
    padding: 0 16px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.create-kb-button {
    width: 100%;
    border-radius: 8px;
    text-transform: none;
    font-weight: 500;
    height: 42px;
    box-shadow: 0 4px 12px rgba(76, 111, 255, 0.2);
}

.menu-item {
    margin: 4px 0;
}

.menu-header {
    display: flex;
    align-items: center;
    padding: 10px 0px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    color: #555;
}

.menu-header-content {
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
}

.menu-header:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.menu-item.active .menu-header {
    background-color: rgba(255, 152, 0, 0.05);
}

.orange-text {
    color: #ff9800;
    font-weight: 500;
}

.submenu {
    padding: 4px 0 8px 0px;
}

.submenu-item {
    padding: 8px 16px;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    border-radius: 6px;
    margin: 2px 0;
    transition: all 0.3s ease;
}

.submenu-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
    color: #333;
}

.submenu-item.active {
    background-color: rgba(26, 115, 232, 0.08);
    color: #E88900;
    font-weight: 500;
}

.submenu-item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

/* 场景历史记录侧边栏样式 */
.scenario-history-sidebar {
    position: absolute;
    left: 80px;
    top: 0;
    width: 320px;
    height: 92vh;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    z-index: 150;
    transition: transform 0.3s ease;
}

.scenario-history-header {
    padding: 16px;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    flex-shrink: 0;
}

.scenario-clear-btn {
    min-width: auto !important;
    padding: 4px 8px !important;
    height: 28px !important;
}

.scenario-clear-btn .v-btn__content {
    font-size: 12px;
}

/* 场景历史记录搜索样式 */
.scenario-search-btn {
    min-width: auto !important;
    padding: 4px !important;
    height: 28px !important;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.scenario-search-btn:hover {
    opacity: 1;
}

.scenario-search-field {
    flex: 1;
}

.scenario-search-field .v-field {
    border-radius: 20px;
    font-size: 14px;
}

.scenario-search-field .v-field__input {
    padding: 8px 12px;
    min-height: 32px;
}

.scenario-search-field .v-field__prepend-inner {
    padding-right: 8px;
}

/* 移动端隐藏文字，只显示图标 */
@media (max-width: 600px) {
    .scenario-clear-btn {
        padding: 4px !important;
        min-width: 28px !important;
    }
}

.scenario-history-groups {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
    height: 100%;
}

/* 场景历史记录分组样式 */
.scenario-history-group {
    margin-bottom: 12px;
}

.scenario-history-group-title {
    padding: 8px 16px;
    font-size: 13px;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scenario-history-group-title:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.scenario-history-count {
    font-size: 12px;
    color: #888;
    margin-left: 4px;
}

.scenario-history-items {
    padding: 4px 0;
}

.scenario-history-item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 0;
    position: relative;
}

.scenario-history-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.scenario-history-item.selected-history {
    background-color: rgba(26, 115, 232, 0.08);
    border-left: 3px solid #1a73e8;
}

.scenario-history-item-main {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    gap: 12px;
}

.scenario-history-text {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.scenario-history-title {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.scenario-history-date {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #888;
    gap: 4px;
}

.scenario-history-menu-btn {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease;
    background-color: transparent;
    border: none;
    color: #666;
}

.scenario-history-item:hover .scenario-history-menu-btn {
    opacity: 1;
}

/* 场景历史记录收起按钮（在面板内） */
.scenario-history-collapse-btn {
    position: absolute;
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 40px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-left: none;
    border-radius: 0 8px 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 160;
}

.scenario-history-collapse-btn:hover {
    background-color: #f5f5f5;
    border-color: #1a73e8;
}

/* 场景历史记录展开按钮（收起状态下） */
.scenario-history-expand-btn {
    position: absolute;
    left: 80px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 40px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-left: none;
    border-radius: 0 8px 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 140;
}

.scenario-history-expand-btn:hover {
    background-color: #f5f5f5;
    border-color: #1a73e8;
}

/* 历史记录侧边栏样式 */
.history-sidebar {
    position: absolute;
    left: 0;
    top: 0;
    width: 320px;
    height: 100vh;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    z-index: 200;
}

.history-header {
    padding: 16px;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    flex-shrink: 0;
}

.new-chat-button-container {
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.new-chat-button {
    border-radius: 8px;
    height: 40px;
    font-weight: 500;
    text-transform: none;
    box-shadow: 0 4px 12px rgba(76, 111, 255, 0.2);
}

.history-groups {
    flex: 1;
    overflow-y: auto;
    /* min-height: 0; 这个很重要，允许flex子项缩小 */
    padding: 8px 0;
    /* overflow: hidden; */
}

.history-group {
    margin-bottom: 12px;
    /* overflow-y: auto; */
    /* height: 72vh; */
}

.history-group-title {
    padding: 8px 16px;
    font-size: 13px;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-count {
    font-size: 12px;
    color: #888;
    margin-left: 4px;
}

.history-items {
    padding: 4px 0;
}

.history-item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 0;
    position: relative;
}

.history-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.selected-history {
    background-color: rgba(26, 115, 232, 0.08);
    border-left: 3px solid #1a73e8;
}

/* 禁用状态的历史记录项 */
.disabled-history {
    cursor: not-allowed !important;
    pointer-events: auto !important;
}

.disabled-history:hover {
    background-color: transparent !important;
    cursor: not-allowed !important;
}

.disabled-history .history-item-main {
    cursor: not-allowed !important;
}

.disabled-history .history-item-main:hover {
    cursor: not-allowed !important;
}

/* 禁用状态的按钮 */
.disabled-btn {
    cursor: not-allowed !important;
}

.disabled-btn:hover {
    cursor: not-allowed !important;
}

/* 确保Vuetify按钮在禁用状态下也显示禁用鼠标样式 */
.v-btn:disabled {
    cursor: not-allowed !important;
}

.v-btn:disabled:hover {
    cursor: not-allowed !important;
}

.history-item-main {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    gap: 12px;
}

.history-text {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.history-title {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.history-date {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #888;
    gap: 4px;
}

.history-menu-btn {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease;
    background-color: transparent;
    border: none;
    color: #666;
}

.history-item:hover .history-menu-btn {
    opacity: 1;
}

.collapse-icon {
    transition: transform 0.3s ease;
}

.collapse-icon.collapsed {
    transform: rotate(-90deg);
}

/* 知识库内容区域样式 */
.main-container {
    /* padding: 0px; */
    background-color: #fff;
    /* height: 300px; */
    /* margin-left: -200px; */
}

/* 知识库模块样式 */
.knowledge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    margin-bottom: 12px;
}

.knowledge-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.knowledge-actions {
    display: flex;
    gap: 12px;
}

.upload-btn {
    border-radius: 8px;
    font-weight: 500;
    text-transform: none;
    letter-spacing: 0.3px;
}

.ai-btn {
    border-radius: 8px;
    font-weight: 500;
    text-transform: none;
    letter-spacing: 0.3px;
}

.knowledge-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 12px;
}

.file-count {
    font-size: 14px;
    color: #666;
}

.knowledge-tools {
    display: flex;
    align-items: center;
    gap: 16px;
}

.select-all-checkbox {
    margin-right: 8px;
}

.tool-icons {
    display: flex;
    gap: 16px;
}

.tool-icon {
    cursor: pointer;
    transition: color 0.3s ease;
}

.tool-icon:hover {
    color: #1a73e8;
}

/* 文件列表新样式 */
.file-list {
    padding: 0;
    /* height: 90vh; */
    overflow-y: auto;
    position: relative;
    scroll-behavior: smooth;
    display: flex;
    flex-direction: column;
    gap: 12px;
    background-color: #f8f9fa;
    height: 80vh;
    /* min-height: 300px; */
}

/* 空文件列表缺省图样式 */
.empty-file-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 60px 0;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(240, 242, 245, 0.5));
    border-radius: 8px;
}

.empty-illustration {
    margin-bottom: 24px;
    position: relative;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-10px);
    }
}

.empty-main-icon {
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

.empty-sub-icon {
    position: absolute;
    bottom: -10px;
    right: -15px;
    filter: drop-shadow(0 2px 4px rgba(255, 184, 0, 0.3));
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 0.8;
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

.empty-text {
    font-size: 22px;
    font-weight: 600;
    color: #555;
    margin-bottom: 12px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.empty-subtext {
    font-size: 15px;
    color: #888;
    max-width: 300px;
    text-align: center;
    line-height: 1.5;
}

.file-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 0px 4px;
    font-size: 14px;
    color: #666;
}

.file-list-header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.batch-actions {
    display: flex;
    gap: 16px;
}

.batch-action-btn {
    width: 24px;
    height: 24px;
    border-radius: 0;
    background-color: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #666;
    padding: 0;
}

.batch-action-btn:hover {
    color: #333;
}

.batch-action-btn .v-icon {
    font-size: 20px;
}

.reference-docs-container {
    margin: 6px 0px 10px 10px;
}

.reference-docs-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.reference-doc-item {
    cursor: pointer;
    padding: 6px 10px;
    width: 170px;
    /* 超出显示省略号 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-radius: 4px;
    background-color: #f5f5f4;
    margin-bottom: 6px;
}

.file-item {
    background-color: #fff;
    border-radius: 2px;
    border: 1px solid #e0e0e0;
    position: relative;
    margin-bottom: 0px;

    /* overflow: hidden; */
}

.file-item-content {
    padding: 16px;
    display: flex;
    gap: 16px;
}

.file-icon {
    flex-shrink: 0;
    width: 34px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #F5F5F4;
}

.file-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-name.clickable {
    color: #1976d2;
    cursor: pointer;
    transition: color 0.2s ease;
}

.file-name.clickable:hover {
    text-decoration: underline;
    color: #1565c0;
}

.warning-icon,
.preview-icon {
    font-size: 16px;
}

/* 文件查看器样式 */
.file-viewer-container {
    display: flex;
    flex-direction: column;
    height: 90vh;
    max-height: 90vh;
}

.file-viewer-content {
    flex: 1;
    overflow: auto;
    /* 改为auto允许滚动 */
    display: flex;
    flex-direction: column;
    min-height: 80vh;
    /* 确保内容区域有足够的高度 */
}

.preview-container {
    width: 100%;
    height: 100%;
    flex: 1;
    overflow: auto;
    /* 允许滚动 */
    display: flex;
    flex-direction: column;
    min-height: 75vh;
    /* 确保容器有足够的高度 */
}

/* 确保officePreview组件内部的元素可以正常滚动 */
.preview-container :deep(.office-preview),
.preview-container :deep(.pdf-preview),
.preview-container :deep(.image-preview),
.preview-container :deep(.text-preview) {
    width: 100%;
    height: 100%;
    min-height: 75vh;
    overflow: auto;
}

/* 保留原有的iframe样式，以防仍然需要 */
.iframe-viewer {
    width: 100%;
    height: 100%;
    flex: 1;
    overflow: auto;
    /* 改为auto允许滚动 */
    display: flex;
    flex-direction: column;
    min-height: 75vh;
    /* 确保iframe容器有足够的高度 */
}

.iframe-viewer iframe {
    width: 100%;
    height: 100%;
    border: none;
    flex: 1;
    min-height: 75vh;
    /* 确保iframe有足够的高度 */
}

.image-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
    padding: 0;
    background-color: #f5f5f5;
    flex: 1;
}

.preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.video-viewer,
.audio-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    background-color: #000;
    flex: 1;
}

.video-viewer video {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
}

.audio-viewer {
    background-color: #f5f5f5;
    padding: 20px;
}

.audio-viewer audio {
    width: 80%;
    max-width: 800px;
}

.file-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    font-size: 14px;
    color: rgba(120, 113, 108, 0.68);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.file-description {
    font-size: 14px;
    color: rgba(120, 113, 108, 0.68);
    line-height: 1.5;
    display: flex;
    /* align-items: center; */
    /* justify-content: center; */
}

.description-label {
    /* width: 140px; */
    /* display: block; */
    color: rgba(120, 113, 108, 0.68);
}

.file-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    font-size: 12px;
    padding: 2px 10px;
    border-radius: 2px;
    background-color: #f0f2f5;
    color: #666;
    cursor: pointer;
}

.tag1 {
    font-size: 12px;
    padding: 2px 10px;
    border-radius: 2px;
    /* background-color: #f0f2f5; */
    color: #666;
}

.file-actions {
    display: flex;
    align-items: center;
    /* flex-direction: column; */
    gap: 8px;
    /* margin-top: 36px; */
}

.action-buttons {
    display: flex;
    gap: 16px;
}

.action-button {
    width: 24px;
    height: 24px;
    border-radius: 0;
    /* background-color: transparent; */
    /* border: none; */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #666;
    padding: 0;
}

.action-button:hover {
    color: #333;
}

.action-button .v-icon {
    font-size: 20px;
}

.file-checkbox {
    margin-left: 8px;
}

.file-status-tags {
    display: flex;
    justify-content: flex-end;
    padding: 4px 16px;
    border-top: 1px solid #f0f0f0;
}

.status-tag {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 2px;
    margin-left: 8px;
}

.status-tag.shared {
    background-color: #E1F5FE;
    color: #0288D1;
}

.status-tag.immediate {
    background-color: #ffebee;
    color: #f44336;
}

/* 聊天容器样式 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    /* 占据整个视口高度 */
    background-color: #f8f9fc;
    position: relative;
    /* 为绝对定位的子元素提供参考 */
}

/* 全高度聊天容器样式 */
.chat-container.full-height {
    height: 100vh;
    margin: 0;
    padding: 0;
}

/* 聊天头部样式 */
.chat-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    flex-shrink: 0;
}

.chat-messages {
    padding: 20px;
    background-color: #f8f9fc;
    background-image: linear-gradient(rgba(255, 255, 255, 0.7) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.7) 1px, transparent 1px);
    background-size: 20px 20px;
    overflow-y: auto;
    overflow-anchor: auto;
    /* 启用滚动锚点，帮助保持滚动位置 */
    width: 100%;
    /* 占据整个容器宽度 */
    flex: 1;
    /* 让消息区域占据剩余空间 */
}

.message {
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
    width: 100%;
    /* 确保消息占据整个宽度 */
    box-sizing: border-box;
    /* 确保padding和border计入宽度 */
}

.message-avatar {
    margin-right: 12px;
    flex-shrink: 0;
}

.message.user {
    flex-direction: row-reverse;
}

.message.user .message-avatar {
    margin-right: 0;
    margin-left: 12px;
}

.message-content {
    max-width: 95%;
    padding: 14px 18px;
    border-radius: 14px;
    background-color: #fff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    color: #333;
    font-size: 15px;
    line-height: 1.6;
    word-break: break-word;
}

.message.user .message-content {
    background-color: #e8f0fe;
    color: #333;
    border-radius: 14px 14px 4px 14px;
    box-shadow: 0 2px 4px rgba(26, 115, 232, 0.1);
}

.message.system .message-content {
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-left: 15px;
    border-radius: 14px 14px 14px 4px;
}

.thinking {
    display: flex;
    align-items: center;
    color: #666;
    font-style: italic;
    padding: 10px 14px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 10px;
}

.thinking-dots {
    display: inline-block;
    width: 24px;
}

.thinking-dots::after {
    content: '';
    animation: thinking-dots 1.5s infinite;
}

.thinking-status {
    margin-right: 10px;
    font-size: 13px;
    color: #1a73e8;
    display: flex;
    align-items: center;
    gap: 4px;
}

.thinking-status .thinking-dots {
    width: 16px;
}

@keyframes thinking-dots {
    0% {
        content: '';
    }

    25% {
        content: '.';
    }

    50% {
        content: '..';
    }

    75% {
        content: '...';
    }

    100% {
        content: '';
    }
}

/* 思考完成提示样式 */
.completed-thinking-hint {
    display: block;
    background-color: #f5f5f5;
    color: #666;
    padding: 8px 10px;
    margin: 0 0 8px 0;
    border-radius: 4px;
    font-size: 13px;
    text-align: left;
    border-left: 3px solid #ccc;
    order: -1;
    /* 确保显示在最上方 */
}

/* 思考步骤容器样式 */
.thinking-steps-container {
    margin-top: 16px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    overflow: visible !important;
    /* 确保内容可见 */
    display: block !important;
    /* 强制显示 */
    opacity: 1 !important;
    visibility: visible !important;
}

.thinking-steps-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 14px;
    background-color: #f5f7fa;
    cursor: pointer;
    transition: background-color 0.2s;
}

.thinking-steps-header:hover {
    background-color: #e8f0fe;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #1a73e8;
}

.thinking-steps {
    padding: 0;
    background-color: #f9f9f9;
    display: block !important;
    /* 确保思考步骤始终可见 */
    opacity: 1 !important;
    visibility: visible !important;
    height: auto !important;
    overflow: visible !important;
    max-height: none !important;
    transition: none !important;
    position: static !important;
    transform: none !important;
    /* 添加CSS变量以控制显示状态，默认始终显示 */
    --thinking-display: block !important;
}

/* 添加特殊样式，确保在历史记录中思考步骤始终可见 */
.thinking-steps.always-visible {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    height: auto !important;
    overflow: visible !important;
    max-height: none !important;
    transition: none !important;
    position: static !important;
    transform: none !important;
    pointer-events: auto !important;
    z-index: 10 !important;
}

/* 思考内容块的样式 */
.thinking-content-block {
    background-color: #f5f7fa;
    border-left: 3px solid #dfe2e5;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 10px;
    color: #333;
    font-size: 14px;
    line-height: 1.5;
}

/* 思考内容整体容器 */
.thinking-all-content {
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 0 0 4px 4px;
}

/* 确保思考容器始终可见，甚至在DOM更新后 */
.message-content .thinking-steps-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
    max-height: none !important;
    transition: none !important;
    position: static !important;
    z-index: 5 !important;
}

.thinking-step {
    padding: 12px 16px;
    border-top: 1px solid #eaeaea;
}

.step-header {
    font-weight: 500;
    margin-bottom: 6px;
    color: #555;
}

.step-content {
    font-size: 14px;
    line-height: 1.5;
    color: #666;
}

.thinking-step.last-step .step-content {
    color: #333;
    font-weight: 500;
}

/* 建议问题样式 */
.suggested-questions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 18px;
}

.suggested-question {
    background-color: #f5f7fa;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 18px;
    padding: 10px 18px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.suggested-question:hover {
    background-color: #e8f0fe;
    border-color: #1a73e8;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(26, 115, 232, 0.15);
}

/* 聊天消息代码块样式 */
.message-avatar .v-avatar {
    background-color: #fff;
    border: 1px solid #e0e0e0;
}

.message.system .message-avatar .v-icon {
    color: #1a73e8;
}

.message.user .message-avatar .v-icon {
    color: #1a73e8;
}

/* 欢迎消息样式 */
.message.system .message-content {
    font-size: 14px;
    line-height: 1.6;
}

/* Markdown内容样式 */
.formal-content {
    line-height: 1.6;
    color: #333;
    font-size: 15px;
}

.formal-content h1,
.formal-content h2,
.formal-content h3,
.formal-content h4,
.formal-content h5,
.formal-content h6 {
    margin: 20px 0 12px 0;
    font-weight: 600;
    line-height: 1.3;
    color: #2c3e50;
}

.formal-content h1 {
    font-size: 28px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.formal-content h2 {
    font-size: 24px;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 8px;
    margin-bottom: 16px;
}

.formal-content h3 {
    font-size: 20px;
    color: #e67e22;
    border-left: 4px solid #e67e22;
    padding-left: 12px;
}

.formal-content h4 {
    font-size: 18px;
    color: #27ae60;
    font-weight: 600;
}

.formal-content h5 {
    font-size: 16px;
    color: #8e44ad;
    font-weight: 600;
}

.formal-content h6 {
    font-size: 14px;
    color: #95a5a6;
    font-weight: 600;
}

/* Markdown段落样式 */
.formal-content p {
    margin: 12px 0;
    line-height: 1.7;
    color: #444;
}

/* Markdown列表样式 */
.formal-content ul,
.formal-content ol {
    margin: 16px 0;
    padding-left: 28px;
}

.formal-content ul.custom-bullet-list,
.formal-content ol.custom-numbered-list {
    margin: 16px 0;
    padding-left: 24px;
}

.formal-content li {
    margin: 8px 0;
    line-height: 1.7;
    color: #444;
}

.formal-content ul li {
    list-style-type: disc;
    position: relative;
}

.formal-content ul li::marker {
    color: #3498db;
    font-size: 1.2em;
}

.formal-content ol li {
    list-style-type: decimal;
    position: relative;
}

.formal-content ol li::marker {
    color: #e74c3c;
    font-weight: 600;
}

/* 嵌套列表样式 */
.formal-content ul ul,
.formal-content ol ol,
.formal-content ul ol,
.formal-content ol ul {
    margin: 8px 0;
    padding-left: 20px;
}

.formal-content ul ul li::marker {
    color: #27ae60;
}

.formal-content ol ol li::marker {
    color: #f39c12;
}

/* Markdown引用样式 */
.formal-content blockquote {
    margin: 20px 0;
    padding: 16px 20px;
    border-left: 5px solid #3498db;
    background: linear-gradient(135deg, #f8f9fc 0%, #e8f4fd 100%);
    color: #2c3e50;
    font-style: italic;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.formal-content blockquote p {
    margin: 0;
    font-size: 16px;
    line-height: 1.6;
}

/* Markdown内联代码样式 */
.formal-content code:not(.hljs) {
    background: linear-gradient(135deg, #f1f2f6 0%, #e8eaf0 100%);
    color: #e74c3c;
    padding: 3px 8px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Menlo', 'Courier New', monospace;
    font-size: 13px;
    font-weight: 500;
    border: 1px solid #ddd;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Markdown链接样式 */
.formal-content a {
    color: #3498db;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
}

.formal-content a:hover {
    border-bottom-color: #3498db;
    color: #2980b9;
    background-color: rgba(52, 152, 219, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
}

/* Markdown图片样式 */
.formal-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 16px 0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
}

.formal-content img:hover {
    transform: scale(1.02);
}

/* Markdown粗体和斜体样式 */
.formal-content strong {
    font-weight: 700;
    color: #2c3e50;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.formal-content em {
    font-style: italic;
    color: #8e44ad;
    font-weight: 500;
}

/* Markdown分隔线样式 */
.formal-content hr {
    border: none;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #e74c3c, #f39c12, #27ae60);
    margin: 32px 0;
    border-radius: 2px;
}

/* Markdown表格样式 */
.formal-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.formal-content th,
.formal-content td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.formal-content th {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.formal-content tr:nth-child(even) {
    background-color: #f8f9fa;
}

.formal-content tr:hover {
    background-color: #e8f4fd;
    transition: background-color 0.3s ease;
}

/* 聊天消息代码块样式 */
.message-content pre {
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
    overflow-x: auto;
    position: relative;
}

.message-content code {
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
}

.message-content .code-block {
    position: relative;
    margin: 16px 0;
}

.message-content .code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f0f2f5;
    border-radius: 8px 8px 0 0;
    border: 1px solid #e0e0e0;
    border-bottom: none;
}

.message-content .code-language {
    font-size: 12px;
    color: #555;
    font-weight: 500;
}

.message-content .code-actions {
    display: flex;
    gap: 8px;
}

.message-content .copy-button,
.message-content .download-button {
    background: none;
    border: none;
    color: #1a73e8;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s;
}

.message-content .copy-button:hover,
.message-content .download-button:hover {
    background-color: rgba(26, 115, 232, 0.1);
}

.message-content .code-content {
    border: 1px solid #e0e0e0;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
}

.message-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;
    overflow-x: auto;
    display: block;
}

.message-content th,
.message-content td {
    border: 1px solid #e0e0e0;
    padding: 8px 12px;
    text-align: left;
}

.message-content th {
    background-color: #f5f7fa;
    font-weight: 500;
}

.message-content tr:nth-child(even) {
    background-color: #fafafa;
}

/* 修改聊天头部样式 */
.chat-container .v-card-title {
    padding: 12px 16px;
    background-color: #fff;
    color: #333;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    font-size: 16px;
    font-weight: 500;
}

.chat-container .v-card-title .v-icon {
    color: #1a73e8;
}

.chat-input {
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    height: 150px;
    /* 固定高度 */
    box-sizing: border-box;
    /* 确保padding计入高度 */
    flex-shrink: 0;
    /* 不允许收缩 */
    width: 100%;
    /* 占据全宽 */
}

.chat-input-container {
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    height: 56px;
    /* 设置一个固定高度 */
}

.mode-selector {
    min-width: 100px;
    flex-shrink: 0;
}

.input-field {
    flex: 1;
}

.input-field :deep(.v-field__field) {
    border-radius: 24px !important;
}

.input-field :deep(.v-field__outline__start),
.input-field :deep(.v-field__outline__end),
.input-field :deep(.v-field__outline__notch) {
    border-color: #e0e0e0 !important;
}

.input-field :deep(.v-field--focused .v-field__outline__start),
.input-field :deep(.v-field--focused .v-field__outline__end),
.input-field :deep(.v-field--focused .v-field__outline__notch) {
    border-color: #1a73e8 !important;
}

.input-field :deep(.v-field__input) {
    padding: 12px 16px;
    font-size: 15px;
}

/* 底部输入行样式 */
.bottom-input-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 12px;
    position: relative;
    min-height: 40px;
}

.kb-selector {
    min-width: 120px;
    flex-shrink: 0;
    display: block !important;
    visibility: visible !important;
}

.kb-label-text {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 联网按钮样式 */
.network-selector {
    min-width: 80px;
    flex-shrink: 0;
    display: block !important;
    visibility: visible !important;
}

.network-btn {
    height: 32px !important;
    min-width: 70px !important;
    font-size: 12px !important;
    border-radius: 16px !important;
    text-transform: none !important;
    letter-spacing: normal !important;
}

.network-btn :deep(.v-btn__content) {
    font-weight: 500;
    font-size: 14px;
}

/* 发送按钮样式 */
.send-button {
    background-color: #1a73e8;
    color: #fff;
    border: none;
    border-radius: 24px;
    padding: 0 20px;
    height: 40px;
    font-weight: 500;
    cursor: pointer;
    margin-left: auto;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(26, 115, 232, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    flex-shrink: 0;
}

.send-button:hover {
    background-color: #0d62d3;
    box-shadow: 0 3px 8px rgba(26, 115, 232, 0.3);
    transform: translateY(-1px);
}

.send-button.processing {
    background-color: #f44336;
    box-shadow: 0 2px 5px rgba(244, 67, 54, 0.2);
}

.input-disclaimer {
    margin-top: 0;
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
    gap: 4px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.mode-select :deep(.v-field__append-inner),
.kb-select :deep(.v-field__append-inner) {
    padding-top: 6px;
}

.mode-select :deep(.v-field),
.kb-select :deep(.v-field) {
    padding-top: 0;
    padding-bottom: 0;
}

/* 自定义下拉选择器样式 */
.mode-select :deep(.v-select__selection),
.kb-select :deep(.v-select__selection) {
    overflow: visible;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .left-sidebar {
        position: fixed;
        left: 0;
        top: 0;
        bottom: 0;
        width: 80px;
        transform: translateX(-80px);
        transition: transform 0.3s ease;
        z-index: 100;
    }

    .left-sidebar.open {
        transform: translateX(0);
    }

    .history-sidebar {
        width: 280px;
        transform: translateX(-280px);
        transition: transform 0.3s ease;
    }

    .history-sidebar.open {
        transform: translateX(0);
    }

    .scenario-history-sidebar {
        width: 280px;
        left: 80px;
    }

    .right-content.with-scenario-history {
        margin-left: 0;
    }

    /* .right-content {
    width: 100%;
    padding-left: 0;
  } */

    .mobile-menu-btn {
        display: flex;
    }

    .sidebar-overlay {
        display: block;
    }

    .file-item {
        flex-direction: column;
    }

    .file-icon {
        margin-right: 0;
        margin-bottom: 12px;
    }

    .file-status {
        margin-left: 0;
        margin-top: 12px;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    /* 移动端适配 */
    .chat-container {
        height: 100vh;
        /* 移动端占据全屏 */
    }

    .chat-input {
        height: 180px;
        /* 移动端增加输入框高度以适应更多内容 */
    }

    .chat-messages {
        width: 100%;
    }

    .right-content {
        padding: 0;
        padding-left: 0;
    }

    /* 聊天输入区域移动端适配 */
    .bottom-input-row {
        flex-wrap: wrap;
        gap: 8px;
    }

    .mode-selector,
    .kb-selector,
    .network-selector {
        min-width: 0;
        flex-basis: calc(50% - 4px);
    }

    /* 内容创作模式下，模式选择器和联网按钮各占一半 */
    .mode-selector:has(+ .network-selector) {
        flex-basis: calc(50% - 4px);
    }

    .network-selector {
        flex-basis: calc(50% - 4px);
    }


    .input-disclaimer {
        order: 3;
        width: 100%;
        margin-top: 8px;
    }

    .send-button {
        order: 2;
        height: 32px;
        padding: 0 12px;
    }
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
    display: none;
    position: absolute;
    top: 16px;
    left: 16px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #1a73e8;
    color: white;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 99;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 遮罩层 */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 90;
}

/* Toast通知样式 */
.toast-container {
    position: fixed;
    top: 100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
}

.toast {
    padding: 12px 20px;
    border-radius: 8px;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    animation: toast-slide-down 0.3s ease-out forwards;
}

@keyframes toast-slide-down {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.toast.success {
    background-color: #1a73e8;
}

.toast.info {
    background-color: #4caf50;
}

.toast.error {
    background-color: #f44336;
}

.toast.warning {
    background-color: #ff9800;
}

/* 返回顶部按钮样式 */
.back-top-content {
    height: 100%;
    width: 100%;
    background-color: #1a73e8;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 聊天区域返回顶部按钮样式 */
.chat-scroll-top-btn {
    position: absolute;
    bottom: 80px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #1a73e8;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
}

.chat-scroll-top-btn:hover {
    background-color: #0d5bbd;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 页面级返回顶部按钮 */
.page-scroll-top-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #4C6FFF;
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(76, 111, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
}

.page-scroll-top-btn:hover {
    background-color: #3b5bdd;
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(76, 111, 255, 0.4);
}

/* 加载更多和刷新提示样式 */
.loading-more,
.refresh-indicator,
.no-more-files {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    color: #666;
    font-size: 14px;
}

.loading-more {
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    margin: 8px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.refresh-text {
    margin-left: 8px;
}

.no-more-files {
    color: #999;
    padding: 16px 0;
    font-size: 13px;
    text-align: center;
    margin-top: 8px;
}

/* Element Plus Skeleton样式覆盖 */
:deep(.el-skeleton) {
    padding: 16px;
}

:deep(.el-skeleton__item) {
    background-color: #f0f2f5;
}

:deep(.el-skeleton__p) {
    height: 16px !important;
}

:deep(.el-skeleton__p.is-last) {
    width: 70% !important;
}

/* 成员管理抽屉样式 */
.member-drawer {
    z-index: 1000;
}

.drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    margin-top: 60px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    background-color: #fff;
}

.drawer-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.drawer-content {
    padding: 20px;
    background-color: #f8f9fa;
}

.search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    flex-wrap: wrap;
    gap: 16px;
}

.search-inputs {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.add-member-btn,
.delete-member-btn {
    height: 40px;
    border-radius: 4px;
    font-weight: 500;
    text-transform: none;
    color: white;
    white-space: nowrap;
}

.members-table {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 4px;
}

.members-table table {
    width: 100%;
    border-collapse: collapse;
}

.members-table th,
.members-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.members-table th {
    font-weight: 500;
    color: #666;
    font-size: 14px;
    white-space: nowrap;
    background-color: #fff;
}

.members-table td {
    font-size: 14px;
    color: #333;
}

.checkbox-cell {
    width: 40px;
}

.action-cell {
    text-align: center;
}

.member-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-text {
    color: #333;
}

.role-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 100px;
    font-size: 12px;
    background-color: #f0f2f5;
    color: #666;
}

.admin-role {
    background-color: rgba(26, 115, 232, 0.1);
    color: #1a73e8;
}

.role-btn {
    text-transform: none;
    letter-spacing: 0;
    margin-left: -10px;
}

.pagination-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    background-color: #fff;
    border-top: 1px solid #eee;
}

.page-size {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
}

.page-size-select {
    width: 60px;
    margin: 0 8px;
}

.page-nav {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: #666;
}

.total-count {
    margin-right: 10px;
}

.pagination-wrapper {
    display: flex;
    align-items: center;
}

.current-page {
    margin: 0 10px;
    min-width: 20px;
    text-align: center;
}

.page-btn {
    min-width: 32px !important;
    width: 32px;
    height: 32px;
}

.goto-wrapper {
    display: flex;
    align-items: center;
}

.goto-page {
    width: 100px;
    margin-right: 8px;
}

@media (max-width: 768px) {
    .search-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-dropdown {
        width: 100%;
    }

    .pagination-controls {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .page-nav {
        flex-wrap: wrap;
        justify-content: center;
        width: 100%;
    }
}

/* 用户按钮样式 */
.user-btn {
    min-width: 36px;
    height: 36px;
    padding: 0 8px;
    margin-left: 8px;
}

/* 添加图标悬停样式 */
.submenu-item-content .v-icon {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.submenu-item-content .v-icon:hover {
    transform: scale(1.2);
}

/* 添加按钮组样式 */
.action-buttons {
    display: flex;
    gap: 10px;
}

.delete-member-btn {
    height: 40px;
    border-radius: 4px;
    font-weight: 500;
    text-transform: none;
    color: white;
}

/* 添加用户对话框样式 */
.add-user-dialog {
    border-radius: 4px;
    z-index: 30000 !important;
    padding: 0px 0px 0px 20px;
    /* overflow: hidden; */
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #eee;
}

.dialog-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.dialog-content {
    padding: 24px;
}

.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.full-width {
    width: 100%;
}

label {
    font-size: 14px;
    color: #333;
}

.required-label::before {
    content: '* ';
    color: #f44336;
}

.radio-group {
    display: flex;
    align-items: center;
    height: 56px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid #eee;
}

.cancel-btn {
    min-width: 80px;
}

.confirm-btn {
    min-width: 80px;
    color: white;
}

@media (max-width: 600px) {
    .form-row {
        flex-direction: column;
    }
}

/* 思考步骤样式 */
.thinking-steps-container {
    margin-top: 16px;
    background-color: #f5f5f5;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
}

.thinking-steps-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 10px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    color: #333;
    transition: background-color 0.2s ease;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 10px;
}

.thinking-status {
    margin-right: 10px;
    font-size: 13px;
    color: #1a73e8;
}

.thinking-dots::after {
    content: '';
    animation: thinking-dots 1.5s infinite;
}

@keyframes thinking-dots {
    0% {
        content: '';
    }

    25% {
        content: '.';
    }

    50% {
        content: '..';
    }

    75% {
        content: '...';
    }

    100% {
        content: '';
    }
}

.thinking-steps-header:hover {
    background-color: #f0f0f0;
}

.thinking-steps-header .v-icon {
    transition: transform 0.3s ease;
}

.thinking-steps {
    padding: 12px 16px;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    height: auto !important;
    overflow: visible !important;
    max-height: none !important;
    transition: none !important;
    position: static !important;
    transform: none !important;
}

.thinking-step {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px dashed #e0e0e0;
}

.thinking-step:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.step-header {
    font-weight: 500;
    color: #555;
    margin-bottom: 8px;
    font-size: 13px;
}

.step-content {
    margin-left: 16px;
    color: #666;
    line-height: 1.5;
}

.last-step {
    border-left: 3px solid #4CAF50;
    padding-left: 12px;
    margin-left: -16px;
}

.last-step .step-header {
    color: #4CAF50;
}

/* 代码块样式 - Atom One Dark风格 */
.code-block {
    margin: 1em auto;
    border: 1px solid #181a1f;
    border-radius: 6px;
    background-color: #282c34;
    overflow: hidden;
    max-width: 800px;
    width: 100%;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #21252b;
    color: #9da5b4;
    font-size: 0.85em;
    border-bottom: 1px solid #181a1f;
}

.code-header-left {
    display: flex;
    align-items: center;
}

.code-language {
    color: #d7dae0;
    font-size: 0.9em;
    font-weight: 500;
}

.code-actions {
    display: flex;
    gap: 8px;
}

.action-button {
    /* background: transparent;
  color: #9da5b4; */
    /* border: 1px solid #181a1f; */
    border-radius: 3px;
    padding: 4px 10px;
    font-size: 0.85em;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
}

.action-button:hover {
    background-color: #2c313a;
    color: #ffffff;
    border-color: #181a1f;
}

.action-button i {
    margin-right: 4px;
    font-size: 1em;
}

.copy-button:active,
.download-button:active,
.run-button:active {
    background-color: #3a404b;
}

.code-pre {
    margin: 0;
    padding: 16px;
    background-color: #282c34;
    overflow-x: auto;
    tab-size: 2;
    -moz-tab-size: 2;
}

.code-pre code {
    font-family: 'Consolas', 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #abb2bf;
    white-space: pre;
    display: block;
    background-color: transparent !important;
    padding: 0;
}

/* 行号和代码行样式 */
.line-numbers-mode {
    counter-reset: line;
    position: relative;
    padding: 8px 0;
}

.line-numbers-mode pre {
    margin: 0;
    padding: 0;
}

.line-numbers-mode code {
    display: block;
    padding: 0;
    margin: 0;
    background-color: #282c34;
    counter-reset: line;
}

.line-numbers-mode .line-number {
    display: inline-block;
    width: 3em;
    text-align: right;
    padding-right: 1em;
    color: #636d83;
    user-select: none;
    border-right: 1px solid #4b5363;
    margin-right: 1em;
}

.line-numbers-mode .line-content {
    display: inline-block;
    white-space: pre-wrap;
    word-break: break-word;
    padding-right: 1em;
    flex-grow: 1;
    min-width: 0;
    overflow-wrap: break-word;
    max-width: 100%;
    box-sizing: border-box;
}

.line-numbers-mode pre code>div {
    display: flex;
    line-height: 1.5;
    padding: 0 0.5em;
}

.line-numbers-mode pre code>div:hover {
    background-color: rgba(255, 255, 255, 0.05);
}



.error-message {
    padding: 10px;
    background-color: #ffebee;
    color: #b71c1c;
    border: 1px solid #ef9a9a;
    border-radius: 4px;
    margin: 10px 0;
}

/* Highlight.js 代码高亮样式 - VS Code风格 */
.hljs {
    display: block;
    overflow-x: auto;
    padding: 0;
    background: #282c34;
    color: #abb2bf;
}

.hljs-comment,
.hljs-quote {
    color: #608b4e;
    font-style: italic;
}

.hljs-doctag,
.hljs-keyword,
.hljs-formula {
    color: #569cd6;
    font-weight: bold;
}

.hljs-section,
.hljs-name,
.hljs-selector-tag,
.hljs-deletion,
.hljs-subst {
    color: #e06c75;
}

.hljs-literal {
    color: #56b6c2;
}

.hljs-string,
.hljs-regexp,
.hljs-addition,
.hljs-attribute,
.hljs-meta-string {
    color: #ce9178;
}

.hljs-built_in,
.hljs-class .hljs-title {
    color: #4ec9b0;
}

.hljs-attr,
.hljs-variable,
.hljs-template-variable,
.hljs-type,
.hljs-selector-class,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-number {
    color: #b5cea8;
}

/* SQL特定样式 */
.language-sql .hljs-keyword {
    color: #569cd6;
    font-weight: bold;
}

.language-sql .hljs-built_in,
.language-sql .hljs-function {
    color: #dcdcaa;
}

.language-sql .hljs-number {
    color: #b5cea8;
}

.language-sql .hljs-string {
    color: #ce9178;
}

.language-sql .hljs-operator {
    color: #d4d4d4;
}

.hljs-symbol,
.hljs-bullet,
.hljs-link,
.hljs-meta,
.hljs-selector-id,
.hljs-title {
    color: #61aeee;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}

.hljs-link {
    text-decoration: underline;
}

/* 用户选择弹窗样式 */
.add-user-dialog {
    border-radius: 4px;
    overflow: hidden;
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;
}

.dialog-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.dialog-content {
    padding: 20px;
}

/* 搜索区域样式 */
.search-area {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px;
}

.search-item {
    display: flex;
    align-items: center;
}

.search-label {
    width: 70px;
    color: #606266;
    font-size: 14px;
}

.search-input {
    width: 180px;
    height: 32px;
    padding: 0 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    outline: none;
    transition: border-color 0.2s;
}

.search-input:focus {
    border-color: #409eff;
}

.search-buttons {
    display: flex;
    gap: 10px;
    margin-left: 10px;
}

.search-btn,
.reset-btn {
    height: 32px;
    padding: 0 15px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.search-btn {
    background-color: #409eff;
    color: white;
}

.reset-btn {
    background-color: #f4f4f5;
    color: #606266;
    border: 1px solid #dcdfe6;
}

/* 表格样式 */
.user-table-container {
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
}

.user-table {
    width: 100%;
    position: relative;
    border-collapse: collapse;
    font-size: 14px;
}

.user-table th,
.user-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #ebeef5;
}

.user-table th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;
}

.user-table tr:hover {
    background-color: #f5f7fa;
}

.user-table input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.status-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 2px;
    font-size: 12px;
}

.status-normal {
    background-color: #f0f9eb;
    color: #67c23a;
}

.status-disabled {
    background-color: #f4f4f5;
    color: #909399;
}

/* 分页区域样式 */
.pagination-area {
    display: flex;
    justify-content: flex-end;
    padding: 12px 16px;
    background-color: #fff;
    border-top: 1px solid #ebeef5;
}

.pagination-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}

.total-text {
    font-size: 14px;
    color: #606266;
}

.page-size-select {
    width: 100px;
}

.goto-text,
.page-text {
    font-size: 14px;
    color: #606266;
}

.goto-input {
    width: 50px;
}

/* 覆盖Element Plus分页组件样式 */
.pagination-wrapper .el-pagination.is-background .el-pager li:not(.is-disabled).is-active {
    background-color: #409eff;
}

.pagination-wrapper .el-pagination {
    --el-pagination-button-width: 32px;
    --el-pagination-button-height: 32px;
}

.pagination-wrapper .el-select .el-input {
    width: 110px;
}

.cancel-button,
.confirm-button {
    padding: 8px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: none;
}

.cancel-button {
    background-color: #f4f4f5;
    color: #606266;
}

.confirm-button {
    background-color: #409eff;
    color: white;
}

.confirm-button:disabled {
    background-color: #a0cfff;
    cursor: not-allowed;
}

/* 添加一些过渡效果 */
.user-table tr {
    transition: background-color 0.2s;
}

.search-btn,
.reset-btn,
.page-btn,
.cancel-button,
.confirm-button {
    transition: all 0.3s;
}

/* 确保El-pagination组件的分页按钮正确显示 */
.el-pagination {
    margin: 15px 0;
    justify-content: center;
    z-index: 3000;
}

.el-pagination .el-pager li {
    z-index: 3001;
}

.el-select-dropdown {
    z-index: 30000 !important;
}

::v-deep .el-scrollbar__view {
    z-index: 30001 !important;
}

::v-deep .el-select-dropdown__list {
    z-index: 30001 !important;
}

/* 添加一些过渡效果 */
.user-table tr {
    transition: background-color 0.2s;
}

.search-btn,
.reset-btn,
.page-btn,
.cancel-button,
.confirm-button {
    transition: all 0.3s;
}

/* 确保所有弹出层的父元素创建新的堆叠上下文 */
.v-dialog,
.v-overlay {
    z-index: 25000 !important;
}

/* 确保分页区域显示在所有内容之上 */
.pagination-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    font-size: 14px;
    position: relative;
    z-index: 10000 !important;
}

.page-size-dropdown {
    position: absolute;
    top: 30px;
    left: 0;
    z-index: 30000;
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: none;
}

/* 选择用户弹窗分页样式 */
.add-user-dialog .pagination-area {
    display: flex;
    justify-content: flex-start;
    padding: 12px 16px;
    background-color: #fff;
    border-top: 1px solid #ebeef5;
}

.add-user-dialog .pagination-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
}

.add-user-dialog .total-text {
    font-size: 14px;
    color: #606266;
    margin-right: 4px;
}

.add-user-dialog .page-size-select {
    width: 110px;
    margin-right: 4px;
}

/* 覆盖Element Plus分页组件样式 */
.add-user-dialog .el-pagination.is-background .el-pager li:not(.is-disabled).is-active {
    background-color: #409eff;
}

.add-user-dialog .el-pagination {
    --el-pagination-button-width: 32px;
    --el-pagination-button-height: 32px;
}

.add-user-dialog .el-pagination .el-input__inner {
    text-align: center;
}

/* 确保跳转到页面的输入框宽度合适 */
.add-user-dialog .el-pagination .el-input {
    width: 50px;
    margin: 0 4px;
}

/* 成员管理抽屉分页样式 */
.pagination-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

.pagination-controls .page-info {
    font-size: 14px;
    color: #606266;
}

.pagination-controls .pagination-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-controls .page-size-btn {
    height: 32px;
    padding: 0 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    color: #606266;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

.pagination-controls .page-size-btn.active {
    color: #409eff;
    border-color: #409eff;
}

.pagination-controls .page-btn {
    min-width: 32px;
    height: 32px;
    padding: 0 4px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    color: #606266;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.pagination-controls .page-btn:hover:not(:disabled) {
    color: #409eff;
    border-color: #409eff;
}

.pagination-controls .page-btn:disabled {
    color: #c0c4cc;
    cursor: not-allowed;
}

.pagination-controls .page-nav-icon {
    font-size: 16px;
    line-height: 1;
}

.pagination-controls .page-info-text {
    font-size: 14px;
    color: #606266;
    margin: 0 4px;
}

.pagination-controls .goto-text,
.pagination-controls .page-text {
    font-size: 14px;
    color: #606266;
}

.pagination-controls .goto-input {
    width: 50px;
    height: 32px;
    text-align: center;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 3px;
    font-size: 14px;
}

.pagination-controls .goto-btn {
    height: 32px;
    padding: 0 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    color: #606266;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

/* 成员管理抽屉搜索栏样式 */
.member-drawer .search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
}

.member-drawer .search-inputs {
    display: flex;
    align-items: center;
    gap: 12px;
}

.member-drawer .search-input {
    width: 260px;
    height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 12px;
    font-size: 14px;
    color: #606266;
}

.member-drawer .search-input:focus {
    outline: none;
    border-color: #409eff;
}

.member-drawer .filter-select {
    width: 120px;
}

.member-drawer .action-buttons {
    display: flex;
    gap: 12px;
}

.member-drawer .action-btn {
    height: 32px;
    padding: 0 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.member-drawer .add-btn {
    background-color: #FFB800;
    color: #fff;
}

.member-drawer .delete-btn {
    background-color: #f44336;
    color: #fff;
}

.member-drawer .action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.file-upload-area {
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    cursor: pointer;
    margin-bottom: 16px;
    transition: all 0.3s;
}

.file-upload-area:hover {
    border-color: #409eff;
    background-color: #f5f7fa;
}

.file-upload-area.drag-over {
    border-color: #409eff;
    background-color: #ecf5ff;
}

.upload-text {
    margin-top: 16px;
}

.upload-text p {
    margin: 4px 0;
}

.upload-hint {
    font-size: 12px;
    color: #909399;
}

.upload-process-info {
    font-size: 14px;
    color: #606266;
    margin-top: 15px;
    font-weight: 500;
}

.upload-process-steps {
    margin-top: 5px;
    padding-left: 25px;
}

.upload-process-steps li {
    font-size: 12px;
    color: #606266;
    margin-bottom: 5px;
    line-height: 1.4;
    position: relative;
}

.upload-process-steps li::marker {
    color: #409eff;
    font-weight: 500;
}

/* 单文件信息样式 */
.selected-file-info {
    margin-top: 16px;
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 12px;
}

.selected-file {
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-name {
    flex: 1;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    color: #909399;
    font-size: 12px;
    margin-right: 8px;
}

/* 上传状态样式 */
.file-upload-status {
    margin: 6px 0;
}

.upload-status-text {
    font-size: 12px;
    color: #1976D2;
    margin-bottom: 4px;
}

/* 结构化处理中的动画效果 */
.upload-status-text.animating {
    color: #f44336;
    font-weight: 500;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }
}

/* 进度条动画效果 */
.upload-progress ::v-deep .v-progress-linear__determinate {
    animation: progress-pulse 2s infinite;
}

@keyframes progress-pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.8;
    }

    100% {
        opacity: 1;
    }
}

.upload-progress-container {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
}

.upload-progress {
    border-radius: 4px;
    flex-grow: 1;
}

.cancel-upload-btn {
    margin-left: 10px;
    white-space: nowrap;
    font-size: 11px;
    height: 24px !important;
}

.retry-btn {
    font-weight: 500;
}

.file-upload-error {
    margin: 6px 0;
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: #FFEBEE;
    padding: 4px 8px;
    border-radius: 4px;
    width: fit-content;
}

.error-icon {
    margin-right: 2px;
}

.error-text {
    font-size: 12px;
    color: #F44336;
    font-weight: 500;
}

/* 自定义列表样式 */
/* 有序列表样式 */
.custom-numbered-list {
    padding-left: 20px;
    margin-left: 20px;
}

.custom-numbered-list li {
    margin-bottom: 10px;
    line-height: 1.5;
    color: #333;
    padding-left: 10px;
}

.custom-numbered-list li::marker {
    color: #1a73e8;
    font-weight: 500;
}

/* 无序列表样式 */
.custom-bullet-list {
    padding-left: 20px;
    margin-left: 20px;
}

.custom-bullet-list li {
    margin-bottom: 10px;
    line-height: 1.5;
    color: #333;
    padding-left: 10px;
}

.custom-bullet-list li::marker {
    color: #1a73e8;
    font-size: 1.2em;
}

/* 文档切片对话框样式 */
.slicing-dialog .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.slicing-dialog .dialog-title {
    font-size: 18px;
    font-weight: 500;
}

.slicing-dialog .dialog-content {
    padding: 20px;
}

.slicing-dialog .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 20px;
    border-top: 1px solid #e0e0e0;
    gap: 12px;
}

.slicing-dialog .cancel-btn,
.slicing-dialog .confirm-btn {
    min-width: 80px;
}

/* Element Plus对话框和下拉菜单样式调整 */
.slicing-dialog-container {
    z-index: 3000 !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 覆盖Element Plus对话框的默认位置，使其居中显示 */
.slicing-dialog-container .el-dialog {
    margin: 15vh auto !important;
    position: relative;
    top: 0 !important;
    transform: none !important;
}

/* 确保模态框覆盖整个页面 */
.slicing-dialog-modal {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

/* 自定义下拉选择框样式 */
.custom-select {
    width: 100%;
    height: 40px;
    padding: 0 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
    color: #606266;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23606266'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
}

.custom-select:focus {
    outline: none;
    border-color: #409eff;
}

.custom-select:disabled {
    background-color: #f5f7fa;
    cursor: not-allowed;
}

.loading-text {
    color: #909399;
    font-size: 14px;
    margin-top: 8px;
    text-align: center;
}

.slicing-dialog-container .el-select {
    width: 100%;
}

/* 确保下拉菜单显示在最顶层 */
.slicing-select-dropdown {
    z-index: 3100 !important;
}

/* 对话框内容样式 */
.dialog-content {
    padding: 10px 0;
}

/* 对话框底部按钮样式 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 聊天容器高亮效果 */
@keyframes highlight-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(26, 115, 232, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(26, 115, 232, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(26, 115, 232, 0);
    }
}

.highlight-section {
    animation: highlight-pulse 1.5s ease-out infinite;
    transition: all 0.3s ease;
    border: 2px solid #1a73e8 !important;
}

/* 返回顶部按钮样式 */
.back-to-top-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #1677ff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 100;
    transition: all 0.3s ease;
}

.back-to-top-btn:hover {
    background-color: #0d5ecf;
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
    .back-to-top-btn {
        width: 40px;
        height: 40px;
        bottom: 20px;
        right: 20px;
    }
}

/* 强制设置思考块样式 - 在任何情况下都确保可见 */
.thinking-block,
div[class*="thinking-block"],
.thinking-steps,
.thinking-steps-container,
.thinking-content-block,
.thinking-all-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
    position: static !important;
    z-index: 10 !important;
    pointer-events: auto !important;
    transform: none !important;
}

/* 特别强调保留思考过程 */
.message-content .thinking-steps-container {
    margin: 10px 0;
    background-color: #f9f9f9;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

/* 思考过程标题样式 */
.thinking-steps-header {
    padding: 8px 12px;
    background-color: #f0f0f0;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    color: #333;
}

/* 思考内容块样式 */
.thinking-content-block {
    margin: 8px 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-left: 3px solid #dfe2e5;
    border-radius: 4px;
    color: #555;
    font-size: 14px;
    line-height: 1.5;
}

/* 正式输出内容样式 */
.formal-content {
    /* margin-top: 12px; */
    /* border-top: 1px solid #eaeaea; */
    color: #333;
    font-size: 15px;
    /* line-height: 1.6; */
}

/* 确保思考步骤容器在正式内容上方 */
.thinking-steps-container {
    order: -1;
    /* 确保在flex布局中排在前面 */
    margin-bottom: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

/* SQL特定样式 */
.language-sql .hljs-keyword {
    color: #569cd6;
    font-weight: bold;
}

.language-sql .hljs-built_in,
.language-sql .hljs-function {
    color: #dcdcaa;
}

.language-sql .hljs-number {
    color: #b5cea8;
}

.language-sql .hljs-string {
    color: #ce9178;
}

.language-sql .hljs-operator {
    color: #d4d4d4;
}

/* 添加SQL代码块特定样式 */
.sql-code .line-content {
    display: block !important;
    white-space: pre-wrap !important;
    word-break: break-word !important;
}

/* 打字指示器样式 */
.typing-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
}

.typing-indicator .dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #2196F3;
    margin: 0 3px;
    animation: typing-animation 1.4s infinite ease-in-out both;
}

.typing-indicator .dot:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator .dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing-animation {

    0%,
    80%,
    100% {
        transform: scale(0);
    }

    40% {
        transform: scale(1);
    }
}

.formal-content-loading {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 8px;
    text-align: center;
}

/* 功能卡片样式 */
.feature-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 24px;
    margin-bottom: 24px;
}

.feature-card {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 12px;
    padding: 0px 20px;
    box-sizing: border-box;
    width: 290px;
    height: 340px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #f0f0f0;
}

.feature-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon-img {
    width: 300px;
    padding-left: 20px;
}

.feature-icon {
    margin-right: 16px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
}

.feature-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.feature-content h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333333;
}

.feature-content p {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #666666;
    line-height: 1.5;
}

.feature-date {
    font-size: 12px;
    color: #999999;
    /* margin-bottom: 12px; */
}

.feature-button {
    align-self: flex-start;
    background-color: #2563EB;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.feature-button:hover {
    background-color: #4a81f7;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .feature-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .feature-cards {
        grid-template-columns: 1fr;
    }

    .feature-card {
        padding: 16px;
    }

    .feature-icon {
        margin-right: 12px;
    }

    .feature-content h3 {
        font-size: 16px;
    }
}

/* Markdown 内容美化样式 */
.markdown-content {
    line-height: 1.6;
    color: #333;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin: 1.5em 0 0.5em 0;
    font-weight: 600;
    line-height: 1.4;
    color: #2c3e50;
}

.markdown-content h1 {
    font-size: 2em;
    border-bottom: 2px solid #eee;
    padding-bottom: 0.3em;
}

.markdown-content h2 {
    font-size: 1.5em;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.3em;
}

.markdown-content h3 {
    font-size: 1.25em;
}

.markdown-content h4 {
    font-size: 1em;
}

.markdown-content h5 {
    font-size: 0.875em;
}

.markdown-content h6 {
    font-size: 0.85em;
    color: #6a737d;
}

.markdown-content p {
    margin: 0.8em 0;
}

.markdown-content ul,
.markdown-content ol {
    margin: 0.8em 0;
    padding-left: 2em;
}

.markdown-content li {
    margin: 0.2em 0;
}

.markdown-content blockquote {
    margin: 1em 0;
    padding: 0.5em 1em;
    border-left: 4px solid #dfe2e5;
    background-color: #f8f9fa;
    color: #6a737d;
}

.markdown-content code {
    background-color: #f3f4f6;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.85em;
    color: #e83e8c;
}

.markdown-content pre {
    background-color: #f8f9fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    overflow-x: auto;
    margin: 1em 0;
}

.markdown-content pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    color: inherit;
}

.markdown-content table {
    border-collapse: collapse;
    margin: 1em 0;
    width: 100%;
}

.markdown-content table th,
.markdown-content table td {
    border: 1px solid #dfe2e5;
    padding: 0.5em 1em;
    text-align: left;
}

.markdown-content table th {
    background-color: #f6f8fa;
    font-weight: 600;
}

.markdown-content a {
    color: #0366d6;
    text-decoration: none;
}

.markdown-content a:hover {
    text-decoration: underline;
}

.markdown-content img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 0.5em 0;
}

.markdown-content hr {
    border: none;
    border-top: 1px solid #e1e4e8;
    margin: 2em 0;
}

/* 强调样式 */
.markdown-content strong {
    font-weight: 600;
    color: #24292e;
}

.markdown-content em {
    font-style: italic;
    color: #586069;
}

/* 列表样式优化 */
.markdown-content ul li {
    list-style-type: disc;
}

.markdown-content ol li {
    list-style-type: decimal;
}

.markdown-content ul ul li {
    list-style-type: circle;
}

.markdown-content ul ul ul li {
    list-style-type: square;
}

/* 文件上传相关样式 */
.file-upload-selector {
    margin-left: 8px;
}

.file-upload-btn {
    min-width: auto !important;
    height: 32px !important;
    font-size: 12px !important;
}

.file-upload-btn .mdi-loading {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.uploaded-files {
    margin-left: 8px;
    margin-top: 8px;
}

.uploaded-file-item {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 12px;
    color: #666;
    max-width: 200px;
}

.uploaded-file-item .file-name {
    flex: 1;
    margin-right: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 120px;
}

.uploaded-file-item .file-size {
    font-size: 10px;
    color: #999;
    margin-right: 4px;
}

.uploaded-file-item:hover {
    background-color: #f0f0f0;
    border-color: #bfbfbf;
}

.file-upload-hint {
    display: flex;
    align-items: center;
    margin-top: 4px;
    font-size: 10px;
    color: #999;
    line-height: 1.2;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .file-upload-selector {
        margin-left: 4px;
    }

    .uploaded-files {
        margin-left: 4px;
        margin-top: 4px;
    }

    .uploaded-file-item {
        max-width: 150px;
    }

    .uploaded-file-item .file-name {
        max-width: 80px;
    }
}