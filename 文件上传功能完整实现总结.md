# 辅助写作模式文件上传功能完整实现总结

## 🎯 核心需求实现

✅ **在辅助写作模式下添加文件上传按钮**
- 位置：联网按钮旁边
- 显示条件：只在辅助写作模式下显示

✅ **联网状态控制**
- 联网模式：文件上传按钮禁用（不可上传）
- 不联网模式：文件上传按钮启用（可以上传）

✅ **单文件限制**
- 只能上传1个文件
- 上传新文件自动替换现有文件

✅ **文件显示样式**
- 按照用户提供的样式实现
- 显示文件名、大小、删除按钮
- 根据文件类型显示不同图标

✅ **Sessions 接口集成**
- 上传文件后自动重新调用 sessions 接口
- 在请求参数中添加 `file` 字段（包含文件内容，不是文件名）
- 文本文件（.txt, .md）：传递文本内容
- 二进制文件（.pdf, .doc, .docx）：传递 Base64 编码内容
- 移除文件后重新调用 sessions 接口（不包含 file 字段）

## 🔧 技术实现详情

### 1. 界面组件
```html
<!-- 文件上传按钮 -->
<div class="file-upload-selector" v-if="isWifi">
    <input type="file" ref="chatFileInput" style="display: none" 
        @change="handleFileSelect" accept=".pdf,.doc,.docx,.txt,.md">
    <v-btn variant="outlined" color="primary" size="small" density="compact"
        :disabled="isProcessing || isNetworkEnabled" @click="triggerFileUpload">
        <v-icon size="x-small" class="mr-1">
            {{ isProcessing ? 'mdi-loading' : 'mdi-paperclip' }}
        </v-icon>
        {{ isProcessing ? '处理中...' : (uploadedFile ? '替换文件' : '上传文件') }}
    </v-btn>
</div>

<!-- 已上传文件显示 -->
<div class="uploaded-files" v-if="uploadedFile && isWifi">
    <div class="uploaded-file-item">
        <v-icon size="small" color="primary" class="mr-1">
            {{ getFileIcon(uploadedFile.name) }}
        </v-icon>
        <span class="file-name">{{ uploadedFile.name }}</span>
        <span class="file-size">{{ formatFileSize(uploadedFile.size) }}</span>
        <v-btn icon size="x-small" variant="text" color="error" 
            @click="removeUploadedFile" :disabled="isProcessing">
            <v-icon size="small">mdi-close-circle</v-icon>
        </v-btn>
    </div>
    <div class="file-upload-hint">
        <span>仅支持上传1个文件，上传新文件将替换当前文件</span>
    </div>
</div>
```

### 2. 核心逻辑
```javascript
// 状态管理
const uploadedFile = ref(null);
const chatFileInput = ref(null);

// 文件上传处理
const handleFileSelect = async (event) => {
    // 1. 文件验证（大小、类型）
    // 2. 更新 uploadedFile 状态
    // 3. 重新创建会话传递文件参数
    await createSession(false); // 保留现有消息
};

// 文件移除处理
const removeUploadedFile = async () => {
    // 1. 清空 uploadedFile 状态
    // 2. 重新创建会话移除文件参数
    await createSession(false); // 保留现有消息
};
```

### 3. Sessions 接口集成
```javascript
// 在 createSession 函数中
if (selectedMode.value.value === 'content_creation') {
    requestData.type = isNetworkEnabled.value ? 'intel' : 'local';
    requestData.kbId = kbId ? String(kbId) : null;

    // 添加文件内容参数
    if (uploadedFile.value && uploadedFile.value.content) {
        requestData.file = uploadedFile.value.content; // 传递文件内容，不是文件名
        console.log('内容创作模式，添加文件内容，文件名:', uploadedFile.value.name, '内容长度:', uploadedFile.value.content.length);
    }
}
```

### 4. 文件内容读取
```javascript
// 读取文件内容
const readFileContent = (file) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = (e) => {
            const result = e.target.result;
            const extension = file.name.split('.').pop().toLowerCase();

            if (extension === 'txt' || extension === 'md') {
                // 文本文件直接返回文本内容
                resolve(result);
            } else {
                // 二进制文件返回 Base64 编码
                resolve(result);
            }
        };

        // 根据文件类型选择读取方式
        const extension = file.name.split('.').pop().toLowerCase();
        if (extension === 'txt' || extension === 'md') {
            reader.readAsText(file, 'UTF-8'); // 文本文件
        } else {
            reader.readAsDataURL(file); // 二进制文件转 Base64
        }
    });
};
```

## 🔄 会话重新创建机制

### 触发时机
1. **文件上传完成后**：自动调用 `createSession(false)`
2. **文件移除后**：自动调用 `createSession(false)`
3. **模式切换清空文件时**：记录状态，后续处理

### 处理流程
1. 设置 `isProcessing = true`
2. 调用 `createSession(false)` 保留现有消息
3. 更新会话 ID 和相关状态
4. 设置 `isProcessing = false`
5. 显示处理结果提示

### 错误处理
- 会话创建失败时显示警告提示
- 不影响文件操作的正常进行
- 保持界面状态一致性

## 📋 文件限制和验证

### 支持的文件格式
- PDF (.pdf)
- Word (.doc, .docx)
- 文本 (.txt)
- Markdown (.md)

### 文件大小限制
- 最大 10MB

### 验证逻辑
```javascript
// 大小验证
const maxSize = 10 * 1024 * 1024; // 10MB
if (file.size > maxSize) {
    showToast('文件大小不能超过10MB', 'error');
    return;
}

// 类型验证
const allowedTypes = ['.pdf', '.doc', '.docx', '.txt', '.md'];
const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
if (!allowedTypes.includes(fileExtension)) {
    showToast('只支持 PDF、Word、TXT、Markdown 格式的文件', 'error');
    return;
}
```

## 🎨 样式和用户体验

### 视觉反馈
- 上传按钮文本动态变化
- 处理中状态显示加载图标
- 文件类型图标显示
- 友好的提示信息

### 交互体验
- 按钮禁用状态管理
- 拖拽上传支持（通过点击触发）
- 文件替换确认提示
- 错误状态处理

## 🧪 测试验证

### 功能测试
1. 模式切换测试
2. 联网状态切换测试
3. 文件上传/替换/删除测试
4. 会话重新创建验证
5. 错误处理测试

### 网络请求验证
- 监控 sessions 接口调用
- 验证 file 字段传递
- 确认会话 ID 更新

## 📝 使用说明

1. 切换到辅助写作模式
2. 关闭联网状态
3. 点击"上传文件"按钮
4. 选择支持的文件格式
5. 系统自动重新创建会话
6. 文件信息显示在界面上
7. 可随时删除或替换文件

## ⚠️ 注意事项

- 文件上传功能仅在辅助写作模式下可用
- 联网模式下禁用文件上传
- 只能上传1个文件，新文件会替换旧文件
- 文件操作会自动重新创建会话
- 切换模式或开启新对话会清空文件
- 文件信息通过 sessions 接口传递给后端
