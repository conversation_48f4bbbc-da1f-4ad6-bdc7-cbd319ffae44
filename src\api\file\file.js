import request from '@/utils/request';
import { DATASET_ID } from '@/config/api.js';

// 获取文件列表
export function getFileList(data) {
    return request({
        url: 'app/api/system/filesApi/list',
        method: 'post',
        data: data
    })
}


// 上传文件
export function uploadFile(data) {
    return request({
        url: `api/v1/datasets/${DATASET_ID}/documents`,
        method: 'post',
        data: data
    })
}

// 新增保存知识库文件
export function addFile(data) {
    return request({
        url: 'app/api/system/filesApi/add',
        method: 'post',
        data: data
    })
}


// 下载文件2
export function downloadFile2(id) {
    return request({
        responseType: 'blob',
        url: `app/api/system/filesApi/download/${id}`,
        method: 'get'
    })
}


// 检查权限chunks
export function chunksDoc(data) {
    return request({
        url: 'app/api/system/ragflowApi/chunksDoc',
        method: 'post',
        data: data
    })
}

// 检查权限
export function checkPermissions(data) {
    return request({
        url: 'app/api/system/filesApi/check-permissions',
        method: 'post',
        data: data
    })
}

// 新上传文件
export function upFile(data, onUploadProgress, repoId, power) {
    return request({
        url: `app/api/system/ragflowApi/${repoId}/doc/save/${power}`, // power传私有还是共享  app/api/system/ragflowApi/${datasetId || DATASET_ID}/doc/save  旧接口
        method: 'post',
        data: data,
        onUploadProgress: onUploadProgress,
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        timeout: 600000,
        maxContentLength: Infinity,
        maxBodyLength: Infinity
    })
}


// 编辑文件
export function editFile(data) {
    return request({
        url: 'app/api/system/filesApi/edit',
        method: 'post',
        data: data
    })
}


// 获取临时文件url
export function getDownloadUrl(FileId) {
    return request({
        url: `app/api/system/filesApi/get-temporary-url/${FileId}`,
        method: 'get'
    })
}

// 删除文件
export function removeFile(data) {
    return request({
        url: 'app/api/system/filesApi/remove',
        method: 'post',
        data: data
    })
}

// 删除文件ragflowApi
export function RemoveRagFile(data) {
    return request({
        url: 'app/api/system/ragflowApi/doc/delete',
        method: 'post',
        data: data
    })
}

// docuDelete最新删除文件
export function docuDelete(data, datasetId) {
    return request({
        url: `/api/v1/datasets/${datasetId}/documents`,
        method: 'DELETE',
        data: data
    })
}

// 删除文件ragflowApi
export function ragDeleteFile(data) {
    return request({
        url: 'app/api/system/ragflowApi/doc/delete',
        method: 'post',
        data: data
    })
}

// 获取文件详情
export function getFileDetail(id, remark) {
    return request({
        url: `app/api/system/filesApi/get/${id}`,
        method: 'get',
        params: remark ? { remark } : {}
    })
}


// 切片列表
export function findSlicing(data) {
    return request({
        url: 'app/api/system/ragflowApi/doc/findSlicing',
        method: 'post',
        data: data
    })
}

// 切片列表文件
export function slicingDoc(data) {
    return request({
        url: 'app/api/system/ragflowApi/doc/slicingDoc',
        method: 'post',
        data: data
    })
}

export function graphGet() {
    return request({
        url: `app/api/system/ragflowApi/doc/graph`,
        method: 'get'
    })
}

// 共享/取消共享
export function docMove(data) {
    return request({
        url: `app/api/system/ragflowApi/doc/move`,
        method: 'post',
        data: data
    })
}


export function docFormat(data) {
    return request({
        url: `app/api/system/ragflowApi/doc/restartFormat`,
        method: 'post',
        data: data
    })
}

export function baiduRollback(timestamp) {
    return request({
        url: `/app/api/system/ragflowApi/baiduRollback/${timestamp}`,
        method: 'get'
    })
}
